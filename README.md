# kyu-admin

## 技术栈
- Vue 3 + Composition API
- Vite
- Ant Design Vue
- Pinia
- Vue Router
- Axios

## 安装依赖
```bash
npm install
```

## 启动项目
```bash
npm run dev
```

## 项目结构
```
src/
  api/         # 接口封装
  components/  # 公共组件
  views/       # 页面
  store/       # Pinia 状态管理
  router/      # 路由配置
  assets/      # 静态资源
  utils/       # 工具函数
main.js        # 入口文件
```

## 关键文件说明
- `src/main.js`：项目入口，初始化 Vue 应用，集成路由、Pinia、Ant Design Vue。
- `src/App.vue`：根组件，渲染路由页面。
- `src/components/Layout.vue`：基础布局，包含侧边栏、顶部栏、内容区。
- `src/router/index.js`：路由配置，主页面均用 Layout 包裹。
- `src/views/Dashboard.vue`：首页，展示统计信息和欢迎语。
- `src/views/User.vue`：用户管理，支持用户列表、mock 增删改查。
- `src/views/Login.vue`：登录页，使用 Ant Design Vue 表单，支持 mock 登录。
- `src/api/auth.js`：mock 登录接口，用户名 admin，密码 123456。
- `src/utils/request.js`：axios 实例封装，便于后续扩展。

## mock 登录说明
- 访问 `/login` 页面，输入用户名 `admin` 和密码 `123456`，点击登录即可模拟登录成功。
- 其他账号密码会提示“用户名或密码错误”。

## 新功能页面说明
- 首页（Dashboard）：展示统计卡片和欢迎信息。
- 用户管理：支持用户列表展示、mock 新增、编辑、删除。
- 所有主页面均通过侧边栏菜单切换。

## 依赖安装提示
如遇依赖未安装，可执行：
```bash
npm install vue@^3.4.0 vue-router@^4.2.0 pinia@^2.1.0 axios@^1.6.0 ant-design-vue@^4.0.0 vite@^5.0.0 @vitejs/plugin-vue@^5.0.0
```
