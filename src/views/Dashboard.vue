<template>
  <div class="dashboard-container">
    <a-row gutter="16">
      <a-col :span="8">
        <a-card title="用户数" bordered>
          <div class="stat">1024</div>
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card title="访问量" bordered>
          <div class="stat">8765</div>
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card title="系统消息" bordered>
          <div class="stat">3</div>
        </a-card>
      </a-col>
    </a-row>
    <a-card style="margin-top: 24px;">
      <h2>欢迎使用 kyu-admin 后台管理系统！</h2>
      <p>这是一个基于 Vue 3 + Vite + Ant Design Vue 的通用后台模板。</p>
    </a-card>
  </div>
</template>

<script setup>
</script>

<style scoped>
.dashboard-container {
  padding: 24px;
}
.stat {
  font-size: 2rem;
  text-align: center;
  margin: 16px 0;
}
</style>
