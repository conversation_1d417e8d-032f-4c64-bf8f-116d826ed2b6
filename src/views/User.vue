<template>
  <div class="user-container">
    <a-card title="用户管理" style="margin-bottom: 16px;">
      <a-button type="primary" @click="showAdd = true">新增用户</a-button>
    </a-card>
    <a-table :columns="columns" :data-source="users" row-key="id" bordered>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" @click="onEdit(record)">编辑</a-button>
            <a-popconfirm title="确定删除？" @confirm="onDelete(record.id)">
              <a-button type="link" danger>删除</a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>
    <a-modal v-model:open="showAdd" title="新增用户" @ok="onAdd">
      <a-form :model="addForm" layout="vertical">
        <a-form-item label="用户名">
          <a-input v-model:value="addForm.username" />
        </a-form-item>
        <a-form-item label="邮箱">
          <a-input v-model:value="addForm.email" />
        </a-form-item>
      </a-form>
    </a-modal>
    <a-modal v-model:open="showEdit" title="编辑用户" @ok="onUpdate">
      <a-form :model="editForm" layout="vertical">
        <a-form-item label="用户名">
          <a-input v-model:value="editForm.username" />
        </a-form-item>
        <a-form-item label="邮箱">
          <a-input v-model:value="editForm.email" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';

const users = ref([
  { id: 1, username: 'admin', email: '<EMAIL>' },
  { id: 2, username: 'user1', email: '<EMAIL>' }
]);

const columns = [
  { title: 'ID', dataIndex: 'id', key: 'id' },
  { title: '用户名', dataIndex: 'username', key: 'username' },
  { title: '邮箱', dataIndex: 'email', key: 'email' },
  { title: '操作', key: 'action' }
];

const showAdd = ref(false);
const showEdit = ref(false);
const addForm = reactive({ username: '', email: '' });
const editForm = reactive({ id: null, username: '', email: '' });

function onAdd() {
  if (!addForm.username || !addForm.email) {
    message.error('请填写完整信息');
    return;
  }
  users.value.push({
    id: Date.now(),
    username: addForm.username,
    email: addForm.email
  });
  showAdd.value = false;
  addForm.username = '';
  addForm.email = '';
  message.success('新增成功');
}

function onEdit(record) {
  editForm.id = record.id;
  editForm.username = record.username;
  editForm.email = record.email;
  showEdit.value = true;
}

function onUpdate() {
  const idx = users.value.findIndex(u => u.id === editForm.id);
  if (idx !== -1) {
    users.value[idx].username = editForm.username;
    users.value[idx].email = editForm.email;
    message.success('更新成功');
  }
  showEdit.value = false;
}

function onDelete(id) {
  users.value = users.value.filter(u => u.id !== id);
  message.success('删除成功');
}
</script>

<style scoped>
.user-container {
  padding: 24px;
}
</style>
