<template>
  <div class="login-container">
    <a-card title="登录" class="login-card">
      <a-form
        :model="form"
        :rules="rules"
        @submit.prevent="onSubmit"
        layout="vertical"
      >
        <a-form-item label="用户名" name="username" :rules="rules.username">
          <a-input v-model:value="form.username" placeholder="请输入用户名" />
        </a-form-item>
        <a-form-item label="密码" name="password" :rules="rules.password">
          <a-input-password v-model:value="form.password" placeholder="请输入密码" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit" block :loading="loading">登录</a-button>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { login } from '@/api/auth';

const router = useRouter();
const loading = ref(false);
const form = reactive({
  username: '',
  password: ''
});
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
};

const onSubmit = async () => {
  loading.value = true;
  try {
    await login(form);
    message.success('登录成功');
    // 登录成功后跳转主页（此处仅演示，实际可跳转 dashboard）
    // router.push('/dashboard');
  } catch (e) {
    message.error(e.message || '登录失败');
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f2f5;
}
.login-card {
  width: 350px;
}
</style>
