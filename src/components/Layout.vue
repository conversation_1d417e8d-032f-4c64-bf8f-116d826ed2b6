<template>
  <a-layout style="min-height: 100vh">
    <a-layout-sider collapsible v-model:collapsed="collapsed">
      <div class="logo">kyu-admin</div>
      <a-menu
        theme="dark"
        mode="inline"
        :selectedKeys="[selectedKey]"
        @click="onMenuClick"
      >
        <a-menu-item key="dashboard">
          <span>首页</span>
        </a-menu-item>
        <a-menu-item key="user">
          <span>用户管理</span>
        </a-menu-item>
      </a-menu>
    </a-layout-sider>
    <a-layout>
      <a-layout-header style="background: #fff; padding: 0 16px;">
        <span>kyu-admin 管理后台</span>
      </a-layout-header>
      <a-layout-content style="margin: 16px">
        <router-view />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';

const collapsed = ref(false);
const router = useRouter();
const route = useRoute();

const selectedKey = computed(() => {
  if (route.path.startsWith('/user')) return 'user';
  return 'dashboard';
});

function onMenuClick({ key }) {
  if (key === 'dashboard') router.push('/dashboard');
  if (key === 'user') router.push('/user');
}
</script>

<style scoped>
.logo {
  height: 32px;
  margin: 16px;
  color: #fff;
  font-size: 20px;
  text-align: center;
  font-weight: bold;
}
</style>
